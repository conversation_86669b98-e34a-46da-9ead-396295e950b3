#!/usr/bin/env python3
"""
Simple Python script that directly replicates the curl command.
"""

import requests
import json

def make_marketplace_request():
    """Direct translation of the curl command to Python."""
    
    url = "https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery"
    
    headers = {
        'accept': 'application/json;api-version=7.2-preview.1;excludeUrls=true',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://marketplace.visualstudio.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://marketplace.visualstudio.com/search?target=VSCode&category=AI&sortBy=Installs',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-requested-with': 'XMLHttpRequest',
        'x-tfs-fedauthredirect': 'Suppress',
        'x-tfs-session': '40c83018-808b-4b5c-beb9-6c0d89c95bd1'
    }
    
    cookies = {
        'VstsSession': '%7B%22PersistentSessionId%22%3A%229945b006-393a-4e79-b7f3-5ebfcbeeb631%22%2C%22PendingAuthenticationSessionId%22%3A%2200000000-0000-0000-0000-000000000000%22%2C%22CurrentAuthenticationSessionId%22%3A%2200000000-0000-0000-0000-000000000000%22%2C%22SignInState%22%3A%7B%7D%7D',
        'Gallery-Service-UserIdentifier': 'c8c72388-a057-442d-bbd1-25063657de9f',
        'Market_SelectedTab': 'vscode',
        'VSCodeOneClickInstallMessageOptOut': 'true',
        '_ga': 'GA1.3.1023046524.1751226526',
        'MSCC': 'NR',
        '_gid': 'GA1.3.150633483.1751753545'
    }
    
    data = {
        "assetTypes": [
            "Microsoft.VisualStudio.Services.Icons.Default",
            "Microsoft.VisualStudio.Services.Icons.Branding",
            "Microsoft.VisualStudio.Services.Icons.Small"
        ],
        "filters": [
            {
                "criteria": [
                    {"filterType": 8, "value": "Microsoft.VisualStudio.Code"},
                    {"filterType": 10, "value": "target:\"Microsoft.VisualStudio.Code\" "},
                    {"filterType": 12, "value": "37888"},
                    {"filterType": 5, "value": "AI"}
                ],
                "direction": 2,
                "pageSize": 54,
                "pageNumber": 2,
                "sortBy": 4,
                "sortOrder": 0,
                "pagingToken": None
            }
        ],
        "flags": 870
    }
    
    response = requests.post(url, headers=headers, cookies=cookies, json=data)
    return response

if __name__ == "__main__":
    response = make_marketplace_request()
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
